#!/usr/bin/env python3
"""
Secure Authentication System for Quotex Trading Bot
Handles secret key validation and secure storage
"""

import os
import hashlib
import base64
import json
from pathlib import Path
from cryptography.fernet import Fernet
from utils import print_colored

class SecureAuth:
    def __init__(self):
        try:
            self.auth_dir = Path("system") / "auth"
            self.auth_dir.mkdir(parents=True, exist_ok=True)
            self.key_file = self.auth_dir / "access.key"
            self.config_file = self.auth_dir / "config.enc"
        except Exception:
            # Fallback to temp directory if can't create in system folder
            import tempfile
            temp_dir = Path(tempfile.gettempdir()) / "quotex_auth"
            temp_dir.mkdir(parents=True, exist_ok=True)
            self.auth_dir = temp_dir
            self.key_file = temp_dir / "access.key"
            self.config_file = temp_dir / "config.enc"

        # The correct secret key (hashed for security)
        self.correct_key_hash = "8b2c3e4d5f6a7b8c9d0e1f2a3b4c5d6e7f8a9b0c1d2e3f4a5b6c7d8e9f0a1b2c"
        
    def _generate_encryption_key(self):
        """Generate or load encryption key"""
        if self.key_file.exists():
            with open(self.key_file, 'rb') as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            with open(self.key_file, 'wb') as f:
                f.write(key)
            # Hide the file (Windows)
            try:
                os.system(f'attrib +h "{self.key_file}"')
            except:
                pass
            return key
    
    def _hash_key(self, key):
        """Hash the secret key for comparison"""
        return hashlib.sha256(key.encode()).hexdigest()
    
    def _encrypt_data(self, data):
        """Encrypt data using Fernet"""
        key = self._generate_encryption_key()
        f = Fernet(key)
        return f.encrypt(json.dumps(data).encode())
    
    def _decrypt_data(self, encrypted_data):
        """Decrypt data using Fernet"""
        key = self._generate_encryption_key()
        f = Fernet(key)
        return json.loads(f.decrypt(encrypted_data).decode())
    
    def save_auth_config(self, config):
        """Save authentication configuration securely"""
        try:
            encrypted_config = self._encrypt_data(config)
            with open(self.config_file, 'wb') as f:
                f.write(encrypted_config)
            # Hide the file (Windows)
            try:
                os.system(f'attrib +h "{self.config_file}"')
            except:
                pass
        except PermissionError:
            # If permission denied, just skip saving (don't fail authentication)
            pass
        except Exception:
            # If any other error, just skip saving
            pass
    
    def load_auth_config(self):
        """Load authentication configuration"""
        if not self.config_file.exists():
            return None
        try:
            with open(self.config_file, 'rb') as f:
                encrypted_data = f.read()
            return self._decrypt_data(encrypted_data)
        except:
            return None
    
    def validate_secret_key(self, input_key):
        """Validate the secret key"""
        # Hash the input key and compare
        input_hash = self._hash_key(input_key)
        
        # For the actual secret key "miketester2390"
        actual_secret = "miketester2390"
        actual_hash = self._hash_key(actual_secret)
        
        return input_hash == actual_hash
    
    def authenticate_user(self):
        """Main authentication function - always asks for secret key"""
        import msvcrt
        import sys

        print_colored("🔐 SECURE ACCESS AUTHENTICATION", "SKY_BLUE", bold=True)
        print_colored("=" * 50, "SKY_BLUE")

        max_attempts = 3
        attempts = 0

        while attempts < max_attempts:
            try:
                print_colored(f"🔑 Enter secret access key (Attempt {attempts + 1}/{max_attempts}):", "INFO")
                print("Secret Key: ", end="", flush=True)

                # Custom password input with asterisks
                secret_key = ""
                while True:
                    char = msvcrt.getch()
                    if char == b'\r':  # Enter key
                        print()  # New line
                        break
                    elif char == b'\x08':  # Backspace
                        if len(secret_key) > 0:
                            secret_key = secret_key[:-1]
                            print('\b \b', end='', flush=True)
                    elif char == b'\x03':  # Ctrl+C
                        print()
                        raise KeyboardInterrupt
                    else:
                        secret_key += char.decode('utf-8', errors='ignore')
                        print('*', end='', flush=True)

                secret_key = secret_key.strip()

                if self.validate_secret_key(secret_key):
                    print_colored("✅ Authentication successful!", "SUCCESS")
                    print_colored("🎉 Access granted to Quotex Trading Bot", "SUCCESS")

                    # Authentication successful - don't worry about saving config
                    return True
                else:
                    attempts += 1
                    remaining = max_attempts - attempts
                    if remaining > 0:
                        print_colored(f"❌ Invalid secret key. {remaining} attempts remaining.", "ERROR")
                    else:
                        print_colored("❌ Maximum attempts exceeded. Access denied.", "ERROR")

            except KeyboardInterrupt:
                print_colored("\n❌ Authentication cancelled by user", "WARNING")
                return False
            except Exception as e:
                # Don't show file permission errors to user
                if "Permission denied" not in str(e):
                    print_colored(f"❌ Authentication error: {e}", "ERROR")
                return False

        print_colored("🚫 Access denied - Invalid credentials", "ERROR")
        return False
    
    def reset_authentication(self):
        """Reset authentication (for testing or security)"""
        try:
            if self.config_file.exists():
                os.remove(self.config_file)
            if self.key_file.exists():
                os.remove(self.key_file)
            print_colored("🔄 Authentication reset successfully", "SUCCESS")
        except Exception as e:
            print_colored(f"❌ Error resetting authentication: {e}", "ERROR")

def simple_authenticate():
    """Simple authentication without file operations"""
    import msvcrt
    import hashlib
    from utils import print_colored

    print_colored("🔐 SECURE ACCESS AUTHENTICATION", "SKY_BLUE", bold=True)
    print_colored("=" * 50, "SKY_BLUE")

    max_attempts = 3
    attempts = 0

    while attempts < max_attempts:
        try:
            print_colored(f"🔑 Enter secret access key (Attempt {attempts + 1}/{max_attempts}):", "INFO")
            print("Secret Key: ", end="", flush=True)

            # Custom password input with asterisks
            secret_key = ""
            while True:
                char = msvcrt.getch()
                if char == b'\r':  # Enter key
                    print()  # New line
                    break
                elif char == b'\x08':  # Backspace
                    if len(secret_key) > 0:
                        secret_key = secret_key[:-1]
                        print('\b \b', end='', flush=True)
                elif char == b'\x03':  # Ctrl+C
                    print()
                    raise KeyboardInterrupt
                else:
                    secret_key += char.decode('utf-8', errors='ignore')
                    print('*', end='', flush=True)

            secret_key = secret_key.strip()

            # Validate secret key
            if secret_key == "miketester2390":
                print_colored("✅ Authentication successful!", "SUCCESS")
                print_colored("🎉 Access granted to Quotex Trading Bot", "SUCCESS")
                return True
            else:
                attempts += 1
                remaining = max_attempts - attempts
                if remaining > 0:
                    print_colored(f"❌ Invalid secret key. {remaining} attempts remaining.", "ERROR")
                else:
                    print_colored("❌ Maximum attempts exceeded. Access denied.", "ERROR")

        except KeyboardInterrupt:
            print_colored("\n❌ Authentication cancelled by user", "WARNING")
            return False
        except Exception:
            return False

    print_colored("🚫 Access denied - Invalid credentials", "ERROR")
    return False

def authenticate_access():
    """Main authentication function to be called from Model.py"""
    try:
        auth_system = SecureAuth()
        return auth_system.authenticate_user()
    except Exception:
        # Fallback simple authentication if SecureAuth fails
        return simple_authenticate()

if __name__ == "__main__":
    # Test the authentication system
    auth = SecureAuth()
    if auth.authenticate_user():
        print_colored("🎯 Authentication test passed!", "SUCCESS")
    else:
        print_colored("❌ Authentication test failed!", "ERROR")
