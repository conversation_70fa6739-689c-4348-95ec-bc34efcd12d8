#!/usr/bin/env python3
"""
Utility functions for the trading bot
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import requests
import json
from config import OANDA_CONFIG, DISPLAY_CONFIG, CURRENCY_PAIRS

def get_color(color_name):
    """Get color code from config"""
    return DISPLAY_CONFIG["COLORS"].get(color_name, "")

def print_colored(text, color="INFO", bold=False, end="\n"):
    """Print colored text"""
    color_code = get_color(color)
    bold_code = get_color("BOLD") if bold else ""
    reset_code = get_color("RESET")
    print(f"{bold_code}{color_code}{text}{reset_code}", end=end)

def print_header(text, width=None):
    """Print a formatted header"""
    if width is None:
        width = DISPLAY_CONFIG["TABLE_WIDTH"]
    
    print_colored("=" * width, "HEADER", bold=True)
    print_colored(text.center(width), "HEADER", bold=True)
    print_colored("=" * width, "HEADER", bold=True)

def print_table_row(columns, widths=None, colors=None):
    """Print a formatted table row"""
    if widths is None:
        widths = [15] * len(columns)
    if colors is None:
        colors = ["INFO"] * len(columns)

    row = ""
    for i, (col, width, color) in enumerate(zip(columns, widths, colors)):
        color_code = get_color(color)
        reset_code = get_color("RESET")
        row += f"{color_code}{str(col):<{width}}{reset_code}"

    print(row)

def get_icon(icon_name):
    """Get icon from config"""
    return DISPLAY_CONFIG["ICONS"].get(icon_name, "")

def print_signal_table_header():
    """Print the signal table header with icons and colors in pipe-separated format"""
    # Print header with proper spacing and pipe separators
    header_line = (
        f"💱 {'PAIR':<15} | "
        f"📅 {'DATE':<15} | "
        f"🕐 {'TIME':<13} | "
        f"📈📉 {'DIRECTION':<11} | "
        f"🎯 {'CONFIDENCE':<11} | "
        f"💰 {'PRICE':<13} | "
        f"🔧 {'STRATEGY':<10}"
    )

    # Print separator line
    separator_line = "=" * 120

    print_colored(separator_line, "HEADER")
    print_colored(header_line, "TEAL", bold=True)
    print_colored(separator_line, "HEADER")

def print_signal_row(date, time, pair, price, signal=None, confidence=None, strategy=None, is_no_signal=False):
    """Print a single signal row in pipe-separated format"""
    # Prepare display values
    if is_no_signal:
        direction_display = "⚪ NO SIGNAL"
        confidence_display = "🎯 -"
        strategy_display = "🔧 -"
        direction_color = "NO_SIGNAL"
    else:
        # Determine signal icon and display
        if signal == "BUY":
            direction_display = "📈 BUY"
            direction_color = "BUY"
        elif signal == "SELL":
            direction_display = "📉 SELL"
            direction_color = "SELL"
        else:
            direction_display = "⚪ NO SIGNAL"
            direction_color = "NO_SIGNAL"

        confidence_display = f"🎯 {confidence}" if confidence else "🎯 -"
        strategy_display = f"🔧 {strategy}" if strategy else "🔧 -"

    # Create the formatted row with pipe separators
    pair_colored = f"{get_color('PAIR')}💱 {pair:<15}{get_color('RESET')}"
    date_colored = f"{get_color('DATE')}📅 {date:<15}{get_color('RESET')}"
    time_colored = f"{get_color('TIME')}🕐 {time:<13}{get_color('RESET')}"
    direction_colored = f"{get_color(direction_color)}{direction_display:<13}{get_color('RESET')}"
    confidence_colored = f"{get_color('CONFIDENCE')}{confidence_display:<13}{get_color('RESET')}"
    price_colored = f"{get_color('PRICE')}💰 {price:<13}{get_color('RESET')}"
    strategy_colored = f"{get_color('STRATEGY')}{strategy_display:<12}{get_color('RESET')}"

    row_line = (
        f"{pair_colored} | "
        f"{date_colored} | "
        f"{time_colored} | "
        f"{direction_colored} | "
        f"{confidence_colored} | "
        f"{price_colored} | "
        f"{strategy_colored}"
    )

    print(row_line)

def print_ml_signal_row(date, time, pair, price, final_signal=None, final_confidence=None,
                       ml_signal=None, ml_confidence=None, rule_signal=None, rule_confidence=None,
                       strategy=None, is_no_signal=False):
    """Print a single ML signal row in pipe-separated format"""
    # Prepare display values
    if is_no_signal:
        final_display = "⚪ NO SIGNAL"
        final_conf_display = "🎯 -"
        ml_display = "🧠 -"
        ml_conf_display = "🎯 -"
        rule_display = "📊 -"
        strategy_display = "🔧 -"
        final_color = "NO_SIGNAL"
        ml_color = "NO_SIGNAL"
        rule_color = "NO_SIGNAL"
    else:
        # Determine final signal display
        if final_signal == "BUY":
            final_display = "📈 BUY"
            final_color = "BUY"
        elif final_signal == "SELL":
            final_display = "📉 SELL"
            final_color = "SELL"
        else:
            final_display = "⚪ NO SIGNAL"
            final_color = "NO_SIGNAL"

        # Determine ML signal display
        if ml_signal == "BUY":
            ml_display = "🧠 BUY"
            ml_color = "BUY"
        elif ml_signal == "SELL":
            ml_display = "🧠 SELL"
            ml_color = "SELL"
        else:
            ml_display = "🧠 HOLD"
            ml_color = "HOLD"

        # Determine rule signal display
        if rule_signal == "BUY":
            rule_display = "📊 BUY"
            rule_color = "BUY"
        elif rule_signal == "SELL":
            rule_display = "📊 SELL"
            rule_color = "SELL"
        else:
            rule_display = "📊 HOLD"
            rule_color = "HOLD"

        final_conf_display = f"🎯 {final_confidence}" if final_confidence else "🎯 -"
        ml_conf_display = f"🎯 {ml_confidence}" if ml_confidence else "🎯 -"
        strategy_display = f"🔧 {strategy}" if strategy else "🔧 -"

    # Create the formatted row with pipe separators (reduced spacing)
    pair_colored = f"{get_color('PAIR')}💱 {pair:<10}{get_color('RESET')}"
    date_colored = f"{get_color('DATE')}📅 {date:<10}{get_color('RESET')}"
    time_colored = f"{get_color('TIME')}🕐 {time:<8}{get_color('RESET')}"
    final_colored = f"{get_color(final_color)}{final_display:<10}{get_color('RESET')}"
    final_conf_colored = f"{get_color('CONFIDENCE')}{final_conf_display:<8}{get_color('RESET')}"
    ml_colored = f"{get_color(ml_color)}{ml_display:<8}{get_color('RESET')}"
    ml_conf_colored = f"{get_color('CONFIDENCE')}{ml_conf_display:<8}{get_color('RESET')}"
    rule_colored = f"{get_color(rule_color)}{rule_display:<8}{get_color('RESET')}"
    price_colored = f"{get_color('PRICE')}💰 {price:<10}{get_color('RESET')}"
    strategy_colored = f"{get_color('STRATEGY')}{strategy_display:<10}{get_color('RESET')}"

    row_line = (
        f"{pair_colored} | "
        f"{date_colored} | "
        f"{time_colored} | "
        f"{final_colored} | "
        f"{final_conf_colored} | "
        f"{ml_colored} | "
        f"{ml_conf_colored} | "
        f"{rule_colored} | "
        f"{price_colored} | "
        f"{strategy_colored}"
    )

    print(row_line)

def print_ml_signal_table_header():
    """Print the ML signal table header with icons and colors in pipe-separated format"""
    # Print header with reduced spacing for ML display
    header_line = (
        f"💱 {'PAIR':<10} | "
        f"📅 {'DATE':<10} | "
        f"🕐 {'TIME':<8} | "
        f"📈📉 {'FINAL':<8} | "
        f"🎯 {'F.CNF':<6} | "
        f"🧠 {'ML':<6} | "
        f"🎯 {'M.CNF':<6} | "
        f"📊 {'RULE':<6} | "
        f"💰 {'PRICE':<10} | "
        f"🔧 {'STRAT':<8}"
    )

    # Print separator line
    separator_line = "=" * 110

    print_colored(separator_line, "HEADER")
    print_colored(header_line, "HEADER", bold=True)
    print_colored(separator_line, "HEADER")

def format_price(price, decimal_places=None):
    """Format price with appropriate decimal places"""
    if decimal_places is None:
        decimal_places = DISPLAY_CONFIG["DECIMAL_PLACES"]
    return f"{float(price):.{decimal_places}f}"

def format_percentage(value):
    """Format percentage value"""
    return f"{float(value):.2f}%"

def get_oanda_headers():
    """Get headers for Oanda API requests"""
    return {
        "Authorization": f"Bearer {OANDA_CONFIG['ACCESS_TOKEN']}",
        "Content-Type": "application/json"
    }

def fetch_live_candles(instrument, count=100, granularity="M1"):
    """Fetch live candle data from Oanda API with specified granularity"""
    headers = get_oanda_headers()

    try:
        url = f"{OANDA_CONFIG['BASE_URL']}/v3/instruments/{instrument}/candles"
        params = {
            "count": count,
            "granularity": granularity,
            "price": "MBA"
        }

        response = requests.get(url, headers=headers, params=params)

        if response.status_code == 200:
            data = response.json()
            return process_candle_data(data['candles'])
        else:
            print_colored(f"❌ Failed to fetch data for {instrument}: {response.status_code}", "ERROR")
            return None

    except Exception as e:
        print_colored(f"❌ Error fetching data for {instrument}: {str(e)}", "ERROR")
        return None

def fetch_historical_candles(instrument, count=1000, granularity="M1"):
    """Fetch historical candle data from Oanda API with specified granularity"""
    headers = get_oanda_headers()

    try:
        url = f"{OANDA_CONFIG['BASE_URL']}/v3/instruments/{instrument}/candles"
        params = {
            "count": min(count, 5000),  # Oanda API limit
            "granularity": granularity,
            "price": "MBA"
        }

        response = requests.get(url, headers=headers, params=params)

        if response.status_code == 200:
            data = response.json()
            return process_candle_data(data['candles'])
        else:
            print_colored(f"❌ Failed to fetch historical data for {instrument}: {response.status_code}", "ERROR")
            return None

    except Exception as e:
        print_colored(f"❌ Error fetching historical data for {instrument}: {str(e)}", "ERROR")
        return None

def process_candle_data(candles):
    """Process raw candle data into DataFrame"""
    processed_data = []
    
    for candle in candles:
        mid = candle['mid']
        
        # Convert time to datetime
        time_str = candle['time'][:19].replace('T', ' ')
        
        processed_data.append({
            'time': time_str,
            'open': float(mid['o']),
            'high': float(mid['h']),
            'low': float(mid['l']),
            'close': float(mid['c']),
            'volume': int(candle['volume']),
            'complete': candle['complete']
        })
    
    df = pd.DataFrame(processed_data)
    
    # Add technical indicators
    df = add_technical_indicators(df)
    
    return df

def add_technical_indicators(df):
    """Add technical indicators to the DataFrame"""
    # RSI
    df['rsi'] = calculate_rsi(df['close'])
    
    # MACD
    df['macd'], df['macd_signal'] = calculate_macd(df['close'])
    
    # Moving averages
    df['sma_20'] = df['close'].rolling(window=20).mean()
    df['ema_12'] = df['close'].ewm(span=12).mean()
    df['ema_26'] = df['close'].ewm(span=26).mean()
    
    # Bollinger Bands
    df['bb_upper'], df['bb_lower'] = calculate_bollinger_bands(df['close'])
    
    # Volume indicators
    df['volume_sma'] = df['volume'].rolling(window=20).mean()
    
    return df

def calculate_rsi(prices, period=14):
    """Calculate RSI indicator"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_macd(prices, fast=12, slow=26, signal=9):
    """Calculate MACD indicator"""
    ema_fast = prices.ewm(span=fast).mean()
    ema_slow = prices.ewm(span=slow).mean()
    macd = ema_fast - ema_slow
    macd_signal = macd.ewm(span=signal).mean()
    return macd, macd_signal

def calculate_bollinger_bands(prices, period=20, std_dev=2):
    """Calculate Bollinger Bands"""
    sma = prices.rolling(window=period).mean()
    std = prices.rolling(window=period).std()
    upper_band = sma + (std * std_dev)
    lower_band = sma - (std * std_dev)
    return upper_band, lower_band

def get_current_time_info():
    """Get current time and calculate seconds until next minute"""
    now = datetime.now()
    seconds_to_next_minute = 60 - now.second

    return {
        'current_time': now,
        'time_str': now.strftime('%Y-%m-%d %H:%M:%S'),
        'seconds_to_next_minute': seconds_to_next_minute
    }

def get_timeframe_time_info(timeframe_key):
    """Get current time and calculate seconds until next timeframe interval"""
    from config import TIMEFRAME_CONFIG

    if timeframe_key not in TIMEFRAME_CONFIG:
        raise ValueError(f"Invalid timeframe: {timeframe_key}")

    config = TIMEFRAME_CONFIG[timeframe_key]
    interval_seconds = config['interval_seconds']

    now = datetime.now()

    # Calculate seconds since start of current interval
    if interval_seconds == 60:  # 1 minute
        seconds_in_interval = now.second
    elif interval_seconds == 120:  # 2 minutes
        seconds_in_interval = (now.minute % 2) * 60 + now.second
    elif interval_seconds == 300:  # 5 minutes
        seconds_in_interval = (now.minute % 5) * 60 + now.second
    elif interval_seconds == 600:  # 10 minutes
        seconds_in_interval = (now.minute % 10) * 60 + now.second
    elif interval_seconds == 900:  # 15 minutes
        seconds_in_interval = (now.minute % 15) * 60 + now.second
    elif interval_seconds == 1800:  # 30 minutes
        seconds_in_interval = (now.minute % 30) * 60 + now.second
    elif interval_seconds == 3600:  # 1 hour
        seconds_in_interval = now.minute * 60 + now.second
    else:
        # Generic calculation for any interval
        total_seconds = now.hour * 3600 + now.minute * 60 + now.second
        seconds_in_interval = total_seconds % interval_seconds

    # Calculate seconds until next interval
    seconds_to_next_interval = interval_seconds - seconds_in_interval

    return {
        'current_time': now,
        'time_str': now.strftime('%Y-%m-%d %H:%M:%S'),
        'seconds_to_next_interval': seconds_to_next_interval,
        'seconds_in_interval': seconds_in_interval,
        'interval_seconds': interval_seconds,
        'timeframe_display': config['display_name']
    }

def validate_pair(pair):
    """Validate if currency pair is supported"""
    return pair in CURRENCY_PAIRS

def select_currency_pairs():
    """Allow user to select currency pairs for trading/analysis"""
    print_colored("\n💱 Available currency pairs:", "INFO", bold=True)
    for i, pair in enumerate(CURRENCY_PAIRS, 1):
        print_colored(f"   {i}. {pair}", "INFO")

    while True:
        print_colored("\nEnter pair numbers (comma-separated) or 'all' for all pairs:", "SUCCESS")
        pairs_input = input("   Pairs: ").strip().lower()

        if pairs_input == 'all':
            selected_pairs = CURRENCY_PAIRS.copy()
            break
        else:
            try:
                pair_indices = [int(x.strip()) for x in pairs_input.split(',')]
                selected_pairs = []

                for idx in pair_indices:
                    if 1 <= idx <= len(CURRENCY_PAIRS):
                        selected_pairs.append(CURRENCY_PAIRS[idx-1])
                    else:
                        print_colored(f"❌ Invalid pair number: {idx}", "ERROR")
                        raise ValueError

                if selected_pairs:
                    break
                else:
                    print_colored("❌ No valid pairs selected", "ERROR")

            except ValueError:
                print_colored("❌ Please enter valid pair numbers", "ERROR")

    print_colored(f"\n✅ Selected pairs: {', '.join(selected_pairs)}", "SUCCESS")
    return selected_pairs

def select_timeframe():
    """Interactive timeframe selection for rule-based trading"""
    from config import TIMEFRAME_CONFIG

    print_header("⏰ TIMEFRAME SELECTION")
    print_colored("Available timeframes:", "INFO", bold=True)
    print()

    # Display timeframes with numbers
    timeframe_keys = list(TIMEFRAME_CONFIG.keys())
    for i, key in enumerate(timeframe_keys, 1):
        config = TIMEFRAME_CONFIG[key]
        print_colored(f"{i}. {config['display_name']} - {config['description']}", "INFO")

    print()
    print_colored("⚠️  Important:", "WARNING", bold=True)
    print_colored("• Bot will fetch data and provide signals according to selected timeframe", "WARNING")
    print_colored("• Signals will be generated 2 seconds before each new candle opens", "WARNING")
    print_colored("• Press Enter for default (1 Minute)", "INFO")
    print()

    while True:
        try:
            selection = input("Select timeframe (1-7): ").strip()

            if not selection:
                # Default selection - 1 minute
                selected_timeframe = "1min"
                break
            else:
                choice = int(selection)
                if 1 <= choice <= len(timeframe_keys):
                    selected_timeframe = timeframe_keys[choice - 1]
                    break
                else:
                    print_colored(f"❌ Please enter a number between 1 and {len(timeframe_keys)}", "ERROR")

        except ValueError:
            print_colored("❌ Please enter a valid number", "ERROR")
        except KeyboardInterrupt:
            print_colored("\n❌ Selection cancelled", "WARNING")
            return None

    config = TIMEFRAME_CONFIG[selected_timeframe]
    print_colored(f"\n✅ Selected timeframe: {config['display_name']}", "SUCCESS")
    print_colored(f"📊 Granularity: {config['granularity']}", "INFO")
    print_colored(f"⏱️  Signal interval: Every {config['interval_seconds']} seconds", "INFO")

    return selected_timeframe

def select_strategies():
    """Interactive strategy selection for rule-based trading"""
    from config import STRATEGY_CONFIG

    print_header("🎯 STRATEGY SELECTION")
    print_colored("Available strategies:", "INFO", bold=True)
    print()

    # Display all strategies with descriptions
    for i, (strategy_id, strategy_info) in enumerate(STRATEGY_CONFIG.items(), 1):
        print_colored(f"{i}. {strategy_id}: {strategy_info['name']}", "BUY", bold=True)
        if 'description' in strategy_info:
            print_colored(f"   📝 {strategy_info['description']}", "INFO")
        if 'accuracy' in strategy_info:
            print_colored(f"   🎯 Accuracy: {strategy_info['accuracy']}", "SUCCESS")
        print()

    print_colored("💡 You can select multiple strategies (e.g., 1,3,5 or 1-4 or 'all')", "WARNING")
    print()

    while True:
        try:
            selection = input("Enter strategy numbers (or 'all' for all strategies): ").strip().lower()

            if selection == 'all':
                selected_strategies = list(STRATEGY_CONFIG.keys())
                break

            # Parse selection
            selected_strategies = []
            parts = selection.split(',')

            for part in parts:
                part = part.strip()
                if '-' in part:
                    # Range selection (e.g., 1-4)
                    start, end = map(int, part.split('-'))
                    strategy_list = list(STRATEGY_CONFIG.keys())
                    for i in range(start-1, min(end, len(strategy_list))):
                        if i >= 0:
                            selected_strategies.append(strategy_list[i])
                else:
                    # Single selection
                    num = int(part)
                    strategy_list = list(STRATEGY_CONFIG.keys())
                    if 1 <= num <= len(strategy_list):
                        selected_strategies.append(strategy_list[num-1])
                    else:
                        raise ValueError(f"Invalid strategy number: {num}")

            # Remove duplicates while preserving order
            selected_strategies = list(dict.fromkeys(selected_strategies))

            if selected_strategies:
                break
            else:
                print_colored("❌ Please select at least one strategy", "ERROR")

        except ValueError as e:
            print_colored(f"❌ Invalid input: {str(e)}", "ERROR")
        except Exception as e:
            print_colored(f"❌ Error: {str(e)}", "ERROR")

    # Display selected strategies
    print_colored(f"\n✅ Selected {len(selected_strategies)} strategies:", "SUCCESS", bold=True)
    for strategy_id in selected_strategies:
        strategy_info = STRATEGY_CONFIG[strategy_id]
        print_colored(f"   • {strategy_id}: {strategy_info['name']}", "SUCCESS")

    return selected_strategies

def format_signal_output(pair, signal_data):
    """Format signal output for display"""
    time_info = get_current_time_info()

    if signal_data['signal'] != 'HOLD':
        color = "BUY" if signal_data['signal'] == 'BUY' else "SELL"
        return {
            'date': time_info['current_time'].strftime('%Y-%m-%d'),
            'time': time_info['current_time'].strftime('%H:%M:%S'),
            'pair': pair,
            'direction': signal_data['signal'],
            'price': format_price(signal_data['price']),
            'confidence': format_percentage(signal_data['confidence'] * 100),
            'strategy': signal_data['strategy'],
            'color': color
        }
    else:
        return {
            'date': time_info['current_time'].strftime('%Y-%m-%d'),
            'time': time_info['current_time'].strftime('%H:%M:%S'),
            'pair': pair,
            'price': format_price(signal_data['price']),
            'message': 'No signal found',
            'color': 'HOLD'
        }
