#!/usr/bin/env python3
"""
PIN Code Handler for Quotex Authentication
Handles PIN code input and validation automatically
"""

import asyncio
import re
import time
from utils import print_colored

class PINHandler:
    def __init__(self):
        self.pin_attempts = 0
        self.max_attempts = 3
        
    def get_pin_from_user(self, message="Enter PIN code: "):
        """Get PIN code from user with validation and hidden input"""
        import getpass

        try:
            print_colored("📧 PIN is required for login: ", "INFO", end="")

            while self.pin_attempts < self.max_attempts:
                try:
                    # Use getpass to hide PIN input with asterisks
                    pin_code = getpass.getpass("").strip()

                    # Validate PIN format (should be 6 digits)
                    if self.validate_pin_format(pin_code):
                        print_colored("✅ Authentication valid", "SUCCESS")
                        return pin_code
                    else:
                        self.pin_attempts += 1
                        remaining = self.max_attempts - self.pin_attempts
                        if remaining > 0:
                            print_colored("❌ Authentication failed", "ERROR")
                            print_colored(f"📧 PIN is required for login ({remaining} attempts remaining): ", "INFO", end="")
                        else:
                            print_colored("❌ Authentication failed", "ERROR")
                            return None

                except KeyboardInterrupt:
                    print_colored("\n❌ PIN input cancelled by user", "WARNING")
                    return None

        except Exception as e:
            print_colored(f"❌ Error getting PIN: {e}", "ERROR")
            return None

        return None
    
    def validate_pin_format(self, pin_code):
        """Validate PIN code format"""
        if not pin_code:
            return False
            
        # Check if it's exactly 6 digits
        if len(pin_code) == 6 and pin_code.isdigit():
            return True
            
        # Check if it's 4-8 digits (some systems use different lengths)
        if 4 <= len(pin_code) <= 8 and pin_code.isdigit():
            return True
            
        return False
    
    def handle_pin_prompt(self, prompt_text):
        """Handle PIN prompt with simplified user experience"""
        return self.get_pin_from_user("Enter the PIN code from your email: ")
    
    def create_auto_pin_handler(self):
        """Create an automatic PIN handler for the login system"""
        async def auto_pin_handler(data, input_message):
            """Automatic PIN handler that integrates with the login system"""
            print_colored("🔐 PIN code required for authentication", "INFO")
            
            # Handle the PIN input
            pin_code = self.handle_pin_prompt(input_message)
            
            if pin_code:
                data["code"] = pin_code
                data["keep_code"] = 1
                return True
            else:
                print_colored("❌ PIN authentication failed", "ERROR")
                return False
                
        return auto_pin_handler

# Global PIN handler instance
pin_handler = PINHandler()

def handle_quotex_pin(prompt_message):
    """Main function to handle Quotex PIN authentication"""
    return pin_handler.handle_pin_prompt(prompt_message)

def create_pin_handler():
    """Create a PIN handler for integration with quotex login"""
    return pin_handler.create_auto_pin_handler()

if __name__ == "__main__":
    # Test the PIN handler
    handler = PINHandler()
    test_message = "Insira o código PIN que acabamos de enviar para o seu e-mail: <EMAIL>"
    result = handler.handle_pin_prompt(test_message)
    if result:
        print_colored(f"✅ PIN handler test passed: {result}", "SUCCESS")
    else:
        print_colored("❌ PIN handler test failed", "ERROR")
